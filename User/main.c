#include "main.h" 

//A0Ϊ��������ת��ʱ��
//A6Ϊ�ϱߣ���ת��ʱ��

float position_1=0,position_2=0;

int main(void)
{
	// ��ʼ��
	USART1_Init(115200);
	USART2_Init(115200);
	
	Key_Init();

	//��ʼ���������
	Stepper_Init();
	
	//�̵�����ʼ��
	Capture_Init();
	
	//�ȹر�ʶ�𣬳�ʼ��DMA
	Discern_end();
	USART2_DMA_Init();

	//��ʼʶ��
	Discern_start();
	
	//������̨λ��
	Stepper_Motor1_SetPosition(position_1);
	Stepper_Motor2_SetPosition(position_2);
	
	printf("Init OK\r\n");
	while (1)
	{
		Key_port();
		
		if(Key[0].data == 1)
		{
			Capture_ON();
			Key[0].data = 0;
		}
		if(Key[1].data == 1)
		{
			Capture_OFF();
			Key[1].data = 0;
		}
		if(Key[3].data == 1)
		{
			Key[3].data = 0;
		}
		
		
		
		//��ȡ���ݸ��±�־
		if(USART_Get_RedFlag())
		{
			//����ʶ����0x15�򿪼��⡢0x25�رռ��⣩
			if(TAG == 0x15)
			{
				Capture_ON();
			}
			else if(TAG == 0x25)
			{
				Capture_OFF();
			}
			else
			{
				//�����������ʶ���ͽ�����̨׷��
				position_1 += ERR_X/1.0;
				position_2 -= ERR_Y/1.0;
				
				printf("x:%d,y:%d\r\n",(int)ERR_X,(int)ERR_Y);
				
				Stepper_Motor1_SetPosition(position_1);
				Stepper_Motor2_SetPosition(position_2);
			}
		}
	}
}

